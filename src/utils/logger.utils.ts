/**
 * Utility functions for formatting console log arguments and extracting error traces.
 * Handles native Error instances, error-like objects, and mixed argument types.
 */

/**
 * Formats console arguments into a string or object for logging.
 * Includes error messages in output while stack traces are handled separately.
 */
export function formatLogArgs(args: any[]): string | object {
  if (args.length === 0) return '';
  if (args.length === 1) {
    const arg = args[0];
    if (arg instanceof Error) return arg.message;
    if (isErrorLike(arg)) return arg.message;
    return arg;
  }

  // Process all arguments, including error messages (but not full Error objects)
  const formattedArgs = args.map((arg) => {
    if (arg instanceof Error) {
      return arg.message;
    }
    if (isErrorLike(arg)) {
      return arg.message;
    }
    if (typeof arg === 'object' && arg !== null) {
      return JSON.stringify(arg);
    }
    return String(arg);
  });

  return formattedArgs.join(' | ');
}

/**
 * Extracts stack trace from Error instances, error-like objects, or serialized errors.
 */
export function extractErrorTrace(args: any[]): string | null {
  for (const arg of args) {
    // Standard Error instances - check stack first
    if (arg instanceof Error) {
      if (arg.stack) {
        return arg.stack;
      }
      // Check if error has a cause with a stack (NestJS HttpException pattern)
      if ('cause' in arg && arg.cause instanceof Error && arg.cause.stack) {
        return arg.cause.stack;
      }
    }

    // Error-like objects with stack property
    if (isErrorLike(arg) && arg.stack) {
      return arg.stack;
    }

    // Plain objects with stack property (serialized errors)
    if (arg && typeof arg === 'object' && typeof arg.stack === 'string') {
      return arg.stack;
    }

    // Check for cause field in plain objects
    if (arg && typeof arg === 'object' && 'cause' in arg) {
      const cause = (arg as any).cause;
      if (cause instanceof Error && cause.stack) {
        return cause.stack;
      }
      if (cause && typeof cause === 'object' && typeof cause.stack === 'string') {
        return cause.stack;
      }
    }
  }

  return null;
}

/**
 * Type guard for error-like objects (e.g., from Axios, Fetch API).
 */
function isErrorLike(obj: any): obj is { message: string; stack?: string } {
  return (
    obj && typeof obj === 'object' && 'message' in obj && typeof obj.message === 'string' && !(obj instanceof Error)
  );
}
