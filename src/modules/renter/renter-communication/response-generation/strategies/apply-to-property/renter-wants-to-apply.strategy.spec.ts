import { Test, TestingModule } from '@nestjs/testing';
import { RenterWantsToApplyStrategy } from './renter-wants-to-apply.strategy';
import { AiService } from '../../../../../ai/ai.service';
import { ActivityFeedService } from '../../../../../investor/activity-feed/activity-feed.service';
import { FollowUpService } from '../../../../../shared/communication/follow-up/follow-up.service';
import { OutboundCommunicationService } from '../../../../../shared/communication/outbound-communication/outbound-communication.service';
import { ActivityFeedEventName } from '../../../../../investor/activity-feed/activity-feed-event.name';
import { LanguageModelsEnum } from '../../../../../ai/enums/language-models.enum';
import { ApplicationProviderEnum } from '../../../../../investor/property/property/enums/application-provider.enum';
import { agreeToApplyToPropertyTemplate } from './prompt/agree-to-apply-to-property.template';
import { proposeToDoShowingFirstTemplate } from './prompt/propose-to-do-showing-first.template';
import { applicationAlreadyRequestedTemplate } from './prompt/application-already-requested.template';
import { FollowUpTypeEnum } from '../../../../../shared/communication/follow-up/enums/follow-up-type.enum';

describe('RenterWantsToApplyStrategy', () => {
  let strategy: RenterWantsToApplyStrategy;
  let aiService: AiService;
  let activityFeedService: ActivityFeedService;
  let followUpService: FollowUpService;
  let outboundCommunicationService: OutboundCommunicationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RenterWantsToApplyStrategy,
        {
          provide: AiService,
          useValue: {
            getResponseWithChatMemory: jest.fn().mockResolvedValue('AI generated response'),
          },
        },
        {
          provide: ActivityFeedService,
          useValue: {
            eventExists: jest.fn(),
            recordEvent: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: FollowUpService,
          useValue: {
            deleteInvestorFollowUpsByInquiry: jest.fn().mockResolvedValue(undefined),
            deleteRenterFollowUpsByType: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: OutboundCommunicationService,
          useValue: {
            sendRenterWantsToApplyNotification: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    strategy = module.get<RenterWantsToApplyStrategy>(RenterWantsToApplyStrategy);
    aiService = module.get<AiService>(AiService);
    activityFeedService = module.get<ActivityFeedService>(ActivityFeedService);
    followUpService = module.get<FollowUpService>(FollowUpService);
    outboundCommunicationService = module.get<OutboundCommunicationService>(OutboundCommunicationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('execute', () => {
    const mockRenterMessage = 'I want to apply for this property';
    const mockConversation = { id: 'conversation-123' };
    const mockRenter = {
      id: 'renter-123',
      user: {
        id: 'user-123',
        name: 'John Doe',
      },
    };
    const mockOwnerUser = {
      id: 'owner-user-123',
      name: 'Jane Owner',
      phoneNumber: '+**********',
    };
    const mockOwner = {
      id: 'owner-123',
      user: Promise.resolve(mockOwnerUser),
    };
    const mockLocation = {
      address: '123 Main St',
      city: 'Test City',
    };
    const mockCompany = {
      id: 'company-123',
    };
    const mockLeaseConditions = {
      applicationProvider: ApplicationProviderEnum.TALLO,
    };
    const mockProperty = {
      id: 'property-123',
      owner: Promise.resolve(mockOwner),
      location: Promise.resolve(mockLocation),
      company: Promise.resolve(mockCompany),
      leaseConditions: Promise.resolve(mockLeaseConditions),
    };
    const mockInquiry = {
      id: 'inquiry-123',
    };

    describe('when application was already requested', () => {
      beforeEach(() => {
        (activityFeedService.eventExists as jest.Mock).mockImplementation((eventName) => {
          if (eventName === ActivityFeedEventName.APPLICATION_REQUESTED) {
            return Promise.resolve(true);
          }
          return Promise.resolve(false);
        });
      });

      it('should call AI service with Tallo provider when applicationProvider is Tallo', async () => {
        const result = await strategy.execute(
          mockRenterMessage,
          mockInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        );

        expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
          {
            input: mockRenterMessage,
            applicationProvider: ApplicationProviderEnum.TALLO,
          },
          mockConversation.id,
          applicationAlreadyRequestedTemplate,
          5,
          LanguageModelsEnum.GPT_4,
        );
        expect(result).toBe('AI generated response');
      });

      it('should call AI service with Other provider when applicationProvider is Other', async () => {
        const mockPropertyWithOtherProvider = {
          ...mockProperty,
          leaseConditions: Promise.resolve({
            applicationProvider: ApplicationProviderEnum.OTHER,
          }),
        };

        const result = await strategy.execute(
          mockRenterMessage,
          mockInquiry as any,
          mockRenter as any,
          mockPropertyWithOtherProvider as any,
          mockConversation as any,
        );

        expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
          {
            input: mockRenterMessage,
            applicationProvider: ApplicationProviderEnum.OTHER,
          },
          mockConversation.id,
          applicationAlreadyRequestedTemplate,
          5,
          LanguageModelsEnum.GPT_4,
        );
        expect(result).toBe('AI generated response');
      });

      it('should default to Tallo provider when applicationProvider is null', async () => {
        const mockPropertyWithNullProvider = {
          ...mockProperty,
          leaseConditions: Promise.resolve({
            applicationProvider: null,
          }),
        };

        await strategy.execute(
          mockRenterMessage,
          mockInquiry as any,
          mockRenter as any,
          mockPropertyWithNullProvider as any,
          mockConversation as any,
        );

        expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
          {
            input: mockRenterMessage,
            applicationProvider: ApplicationProviderEnum.TALLO,
          },
          mockConversation.id,
          applicationAlreadyRequestedTemplate,
          5,
          LanguageModelsEnum.GPT_4,
        );
      });
    });

    describe('when showing was completed but application not yet requested', () => {
      beforeEach(() => {
        (activityFeedService.eventExists as jest.Mock).mockImplementation((eventName) => {
          if (eventName === ActivityFeedEventName.SHOWING_COMPLETED) {
            return Promise.resolve(true);
          }
          return Promise.resolve(false);
        });
      });

      it('should send application invite and return AI response', async () => {
        const result = await strategy.execute(
          mockRenterMessage,
          mockInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        );

        expect(followUpService.deleteInvestorFollowUpsByInquiry).toHaveBeenCalledWith(
          mockInquiry,
          mockOwnerUser.id,
          FollowUpTypeEnum.POST_SHOWING_INVESTOR_FOLLOW_UP,
        );
        expect(followUpService.deleteRenterFollowUpsByType).toHaveBeenCalledWith(
          mockConversation.id,
          FollowUpTypeEnum.POST_SHOWING_RENTER_FOLLOW_UP,
        );
        expect(outboundCommunicationService.sendRenterWantsToApplyNotification).toHaveBeenCalledWith(
          mockProperty,
          mockLocation,
          mockOwnerUser,
          mockRenter,
        );
        expect(activityFeedService.recordEvent).toHaveBeenCalledWith(
          ActivityFeedEventName.APPLICATION_REQUESTED,
          mockCompany.id,
          {
            userId: mockRenter.user.id,
            propertyId: mockProperty.id,
            renterId: mockRenter.id,
          },
        );
        expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
          {
            input: mockRenterMessage,
          },
          mockConversation.id,
          agreeToApplyToPropertyTemplate,
          5,
          LanguageModelsEnum.GPT_4_MINI,
        );
        expect(result).toBe('Apply to property: AI generated response');
      });
    });

    describe('when showing was not completed', () => {
      beforeEach(() => {
        (activityFeedService.eventExists as jest.Mock).mockResolvedValue(false);
      });

      it('should propose to do showing first', async () => {
        const result = await strategy.execute(
          mockRenterMessage,
          mockInquiry as any,
          mockRenter as any,
          mockProperty as any,
          mockConversation as any,
        );

        expect(aiService.getResponseWithChatMemory).toHaveBeenCalledWith(
          {
            input: mockRenterMessage,
          },
          mockConversation.id,
          proposeToDoShowingFirstTemplate,
          5,
          LanguageModelsEnum.CLAUDE_4_SONNET,
        );
        expect(result).toBe('Apply to property: AI generated response');
      });
    });
  });
});
