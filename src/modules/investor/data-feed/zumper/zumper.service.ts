import { convert } from 'xmlbuilder2';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LeadsService } from '../../leads/leads.service';
import { ZumperNewLead } from './model/zumper-new-lead.dto';
import { LeadDto } from '../../leads/models/lead.dto';
import { PropertyInquirySource } from '../../property-inquiry/property-inquiry-source.enum';
import ParsingUtils from '../../../../utils/parsing.utils';

import { Property } from '../../property/property/entities/property.entity';
import { DataFeed } from '../data-feed.entity';
import { Amenities } from '../../property/property/property-details/amenities/amenities.entity';
import { IncludedUtilities } from '../../property/property/property-details/included-utilities/included-utilities.entity';
import { Parking } from '../../property/property/property-details/parking/parking.entity';
import { PetPolicy } from '../../property/property/property-details/pet-policy/pet-policy.entity';
import { PetPolicyEnum } from '../../property/property/property-details/pet-policy/pet-policy.enum';
import { PropertySpecifications } from '../../property/property/property-details/specifications/property-specifications.entity';
import PropertyUtils from '../../property/utils/property-utils';
import { PropertyTypeEnum } from '../../property/property/enums/property-type.enum';
import { ParkingType } from '../../property/property/property-details/parking/parking.type';
import { HeatingSystemEnum } from '../../property/property/enums/heat-system.enum';
import { CoolingSystem } from '../../property/property/enums/cooling-system.enum';
import { FloorCovering } from '../../property/property/enums/floor-covering.enum';
import { ArchitectureStyle } from '../../property/property/enums/architecture-style.enum';
import { ExteriorType } from '../../property/property/enums/exterior-type.enum';
import { RoofType } from '../../property/property/enums/roof-type.enum';

@Injectable()
export class ZumperService {
  constructor(
    private readonly config: ConfigService,
    private readonly leadsService: LeadsService,
  ) {}

  async prepareXml(feedItems: DataFeed[]): Promise<string> {
    return convert(await this.prepareXmlObject(feedItems));
  }

  private async prepareXmlObject(feedItems: DataFeed[]) {
    const listings = await Promise.all(feedItems.map((item) => this.getListingObject(item)));

    // Build companies: always Tallo, plus unique agent companies for branded items
    const companies: any[] = [
      {
        SourceCompanyId: 'tallo',
        CompanyName: 'Tallo',
        City: 'New York',
        StateOrProvince: 'NY',
        WebSite: 'https://tallo.ai',
        CompanyLogo: 'https://files.tallo.ai/images/1f573e82-d436-4fd0-8783-c9db51627231.png',
      },
    ];

    const seenCompanyIds = new Set<string>();
    for (const item of feedItems) {
      if (item.syndicateAsAgent) {
        const company = await item.property.company;
        const owner = await item.property.owner;
        const user = await owner.user;
        if (company.id && !seenCompanyIds.has(company.id)) {
          seenCompanyIds.add(company.id);
          companies.push({
            SourceCompanyId: company.id,
            CompanyName: company.name,
            City: owner.city || 'New York',
            StateOrProvince: owner.state || 'NY',
            WebSite: company.website || 'https://tallo.ai',
            CompanyLogo: user.avatar || 'https://files.tallo.ai/images/1f573e82-d436-4fd0-8783-c9db51627231.png',
          });
        }
      }
    }

    return {
      TalloItems: {
        Company: companies.length === 1 ? companies[0] : companies,
        Listing: listings,
      },
    };
  }

  private async getListingObject(item: DataFeed): Promise<any> {
    const property: Property = item.property;
    const propertySpecifications = await property.specifications;
    const propertyLocation = await property.location;
    const leaseConditions = await property.leaseConditions;
    const amenities = await property.amenities;
    const petPolicy = await property.petPolicy;
    const parking = await property.parking;
    const includedUtilities = await property.includedUtilities;

    // Determine branding and contact info
    let companyId = 'tallo';
    let contactName = 'Emily';
    const contactEmail = this.config.get('TALLO_SENDER_EMAIL');
    let contactPhone = '5555555555'; // Default phone number

    if (item.syndicateAsAgent) {
      const company = await property.company;
      companyId = company.id;
      const owner = await property.owner;
      const user = await owner.user;
      contactName = user.name;
      contactPhone = item.syndicateAgentNumber ? user.phoneNumber : contactPhone;
    }

    // Build listing object
    const listing: any = {
      ListingKey: property.id,
      PropertyType: this.getZumperPropertyType(propertySpecifications.propertyType),
      Street: propertyLocation.address,
      City: propertyLocation.city,
      State: propertyLocation.state,
      Zip: propertyLocation.zip,
      LastModifiedTimestamp: this.formatDateToISO8601(property.updatedAt),
      Price: leaseConditions.rent,
      Bedrooms: propertySpecifications.bedrooms || 0,
      BathsFull: propertySpecifications.fullBathrooms || 0,
      BathsHalf: propertySpecifications.halfBathrooms || 0,
      BathsOneQuarter: 0,
      BathsThreeQuarter: 0,
      ShowingContactEmail: contactEmail,
      ShowingContactPhone: contactPhone,
    };

    // Optional fields
    if (propertyLocation.isAddressHidden) {
      listing.ShowAddress = false;
    }

    if (propertyLocation.apartmentNumber) {
      listing.Unit = propertyLocation.apartmentNumber;
    }

    if (property.description) {
      listing.Description = property.description;
    }

    if (leaseConditions.applicationFee) {
      listing.ApplicationFee = leaseConditions.applicationFee;
    }

    if (leaseConditions.securityDeposit) {
      listing.SecurityDeposit = PropertyUtils.getDeposit(leaseConditions.securityDeposit, leaseConditions.rent);
    }

    if (leaseConditions.desiredLeasingDate) {
      listing.AvailabilityDate = this.formatToYYYYMMDD(leaseConditions.desiredLeasingDate);
    }

    if (propertySpecifications.squareFeet) {
      listing.TotalAreaSqft = propertySpecifications.squareFeet;
    }

    if (contactName) {
      listing.ShowingContactName = contactName;
    }

    if (propertySpecifications.yearBuilt) {
      listing.YearBuilt = propertySpecifications.yearBuilt;
    }

    if (amenities?.isFurnished) {
      listing.FurnishedYN = true;
    }

    if (property.allowsSmoking !== null && property.allowsSmoking !== undefined) {
      listing.SmokingFreeYN = !property.allowsSmoking;
    }

    // Add rent includes
    const rentIncludes = this.getRentIncludes(includedUtilities);
    if (rentIncludes) {
      listing.RentIncludes = rentIncludes;
    }

    // Add architecture style
    if (propertySpecifications.architectureStyle) {
      const archStyle = this.getZumperArchitectureStyle(propertySpecifications.architectureStyle);
      if (archStyle) {
        listing.ArchitecturalStyle = archStyle;
      }
    }

    // Add exterior type
    if (propertySpecifications.exteriorType) {
      const extType = this.getZumperExteriorType(propertySpecifications.exteriorType);
      if (extType) {
        listing.BuildingExteriorType = extType;
      }
    }

    // Add roof type
    if (propertySpecifications.roofType) {
      const roofType = this.getZumperRoofType(propertySpecifications.roofType);
      if (roofType) {
        listing.Roof = roofType;
      }
    }

    // Add view
    if (propertySpecifications.viewType) {
      listing.View = propertySpecifications.viewType;
    }

    // Add photos
    const photos = await this.getListingPhotos(property);
    if (photos.length > 0) {
      listing.ListingPhoto = photos;
    }

    // Add cooling features
    const coolingFeatures = this.getCoolingFeatures(propertySpecifications);
    if (coolingFeatures.length > 0) {
      listing.CoolingFeatures = { Cooling: coolingFeatures };
    }

    // Add heating features
    const heatingFeatures = this.getHeatingFeatures(propertySpecifications);
    if (heatingFeatures.length > 0) {
      listing.HeatingFeatures = { Heating: heatingFeatures };
    }

    // Add pet policy
    const petPolicyObj = this.getPetPolicy(petPolicy, leaseConditions);
    if (petPolicyObj) {
      listing.PetPolicy = petPolicyObj;
    }

    // Add parking
    const parkingTypes = this.getParkingTypes(parking);
    if (parkingTypes.length > 0) {
      listing.ParkingTypes = { Parking: parkingTypes };
    }

    // Add appliances
    const appliances = this.getAppliances(amenities);
    if (appliances.length > 0) {
      listing.Appliances = { Appliance: appliances };
    }

    // Add amenities
    const amenitiesList = this.getAmenitiesList(amenities);
    if (amenitiesList.length > 0) {
      listing.Amenities = { Amenity: amenitiesList };
    }

    // Add rooms
    const rooms = this.getRooms(propertySpecifications);
    if (rooms.length > 0) {
      listing.Rooms = { Room: rooms };
    }

    // Add flooring
    const flooring = this.getFlooringFeatures(propertySpecifications);
    if (flooring.length > 0) {
      listing.FlooringFeatures = { Flooring: flooring };
    }

    return listing;
  }

  private getZumperPropertyType(propertyType: PropertyTypeEnum): string {
    // Map internal property types to Zumper's enum
    switch (propertyType) {
      case PropertyTypeEnum.HOUSE:
        return 'Single Family Home';
      case PropertyTypeEnum.CONDO:
        return 'Condo';
      case PropertyTypeEnum.TOWNHOUSE:
        return 'Townhome';
      case PropertyTypeEnum.APARTMENT:
      case PropertyTypeEnum.STUDIO:
        return 'Apartment';
      case PropertyTypeEnum.DUPLEX:
        return 'Duplex';
      case PropertyTypeEnum.ROOM:
        return 'Single Family Home'; // Default to single family for rooms
      default:
        return 'Apartment';
    }
  }

  private async getListingPhotos(property: Property): Promise<any[]> {
    const propertyImages = await property.propertyImages;
    const photos: any[] = [];
    let sequence = 1;

    // Add cover image first
    if (property.coverImage) {
      photos.push({
        Sequence: sequence++,
        PhotoUrl: property.coverImage,
      });
    }

    // Add other images
    const sortedImages = propertyImages
      .filter((pi) => pi.file !== null)
      .map((pi) => pi.file)
      .sort((a, b) => a.order - b.order);

    for (const image of sortedImages) {
      photos.push({
        Sequence: sequence++,
        PhotoUrl: image.url,
      });
    }

    return photos;
  }

  private getRentIncludes(includedUtilities: IncludedUtilities): string | null {
    if (!includedUtilities) {
      return null;
    }

    const utilities: string[] = [];

    if (includedUtilities.water) utilities.push('Water');
    if (includedUtilities.sewage) utilities.push('Sewer');
    if (includedUtilities.garbage) utilities.push('Trash');
    if (includedUtilities.electricity) utilities.push('Electricity');
    if (includedUtilities.gas) utilities.push('Gas');
    if (includedUtilities.internet) utilities.push('Internet');
    if (includedUtilities.cable) utilities.push('Cable');

    return utilities.length > 0 ? utilities.join(', ') : null;
  }

  private getCoolingFeatures(specs: PropertySpecifications): string[] {
    const features: string[] = [];

    if (!specs || !specs.coolingSystem) {
      return features;
    }

    switch (specs.coolingSystem) {
      case CoolingSystem.CENTRAL:
        features.push('Central Air');
        break;
      case CoolingSystem.EVAPORATIVE:
        features.push('Evaporative');
        break;
      case CoolingSystem.GEOTHERMAL:
        features.push('Geothermal');
        break;
      case CoolingSystem.SOLAR:
        features.push('Solar');
        break;
      case CoolingSystem.WALL:
        features.push('Wall');
        break;
    }

    return features;
  }

  private getHeatingFeatures(specs: PropertySpecifications): string[] {
    const features: string[] = [];

    if (!specs || !specs.heatingSystem) {
      return features;
    }

    switch (specs.heatingSystem) {
      case HeatingSystemEnum.BASEBOARD:
        features.push('Baseboard');
        break;
      case HeatingSystemEnum.FORCED_AIR:
        features.push('Forced Air');
        break;
      case HeatingSystemEnum.HEAT_PUMP:
        features.push('Heat Pump');
        break;
      case HeatingSystemEnum.RADIANT:
        features.push('Radiant');
        break;
      case HeatingSystemEnum.STOVE:
        features.push('Stove');
        break;
      case HeatingSystemEnum.WALL:
        features.push('Wall');
        break;
    }

    return features;
  }

  private getPetPolicy(petPolicy: PetPolicy, leaseConditions: any): any | null {
    if (!petPolicy || !petPolicy.allowsPets) {
      return null;
    }

    const policy: any = {
      PetsAllowedYN: true,
    };

    const dogsAllowed =
      petPolicy.smallDogsPolicy === PetPolicyEnum.ALLOWED || petPolicy.largeDogsPolicy === PetPolicyEnum.ALLOWED;
    policy.DogsAllowedYN = dogsAllowed;

    if (petPolicy.catsPolicy === PetPolicyEnum.ALLOWED) {
      policy.CatsAllowedYN = true;
    }

    if (petPolicy.smallDogsPolicy === PetPolicyEnum.ALLOWED) {
      policy.SmallDogsAllowedYN = true;
    }

    if (petPolicy.largeDogsPolicy === PetPolicyEnum.ALLOWED) {
      policy.LargeDogsAllowedYN = true;
    }

    if (petPolicy.petRent || petPolicy.petDeposit) {
      policy.PetFeeYN = true;
      if (petPolicy.petRent) {
        policy.PetFeePrice = petPolicy.petRent;
      } else if (petPolicy.petDeposit) {
        policy.PetFeePrice = petPolicy.petDeposit;
      }
    }

    return policy;
  }

  private getParkingTypes(parking: Parking): string[] {
    const types: string[] = [];

    if (!parking || !parking.hasParking) {
      return ['None'];
    }

    if (parking.parkingType) {
      switch (parking.parkingType) {
        case ParkingType.CARPORT:
          types.push('Carport');
          break;
        case ParkingType.GARAGE_ATTACHED:
        case ParkingType.GARAGE_DETACHED:
          types.push('Garage');
          break;
        case ParkingType.OFF_STREET:
          types.push('Off Street');
          break;
        case ParkingType.ON_STREET:
          types.push('On-Street');
          break;
        case ParkingType.NONE:
          types.push('None');
          break;
      }
    }

    return types.length > 0 ? types : ['None'];
  }

  private getAppliances(amenities: Amenities): string[] {
    const appliances: string[] = [];

    if (!amenities) {
      return appliances;
    }

    if (amenities.hasDishwasher || amenities.hasDishWasher) appliances.push('Dishwasher');
    if (amenities.hasWasher) appliances.push('Washer');
    if (amenities.hasDryer) appliances.push('Dryer');
    if (amenities.hasFreezer) appliances.push('Freezer');
    if (amenities.hasGarbageDisposal) appliances.push('Garbage Disposal');
    if (amenities.hasMicrowave) appliances.push('Microwave');
    if (amenities.hasRangeOven) appliances.push('Range');
    if (amenities.hasRefrigerator) appliances.push('Refrigerator');
    if (amenities.hasTrashCompactor) appliances.push('Trash Compactor');

    return appliances;
  }

  private getAmenitiesList(amenities: Amenities): string[] {
    const list: string[] = [];

    if (!amenities) {
      return list;
    }

    if (amenities.hasBarbecueArea) list.push('Barbecue Area');
    if (amenities.hasBasement) list.push('Basement');
    if (amenities.hasBasketBallCourt) list.push('Basketball Court');
    if (amenities.hasBusinessCenter) list.push('Business Center');
    if (amenities.hasCableSatellite) list.push('Cable Satellite');
    if (amenities.hasChildCare) list.push('Child Care');
    if (amenities.hasClubDiscount) list.push('Club Discount');
    if (amenities.hasConcierge) list.push('Concierge');
    if (amenities.hasControlledAccess) list.push('Controlled Access');
    if (amenities.hasCourtyard) list.push('Courtyard');
    if (amenities.hasDeck) list.push('Deck');
    if (amenities.hasDisabledAccess) list.push('Disabled Access');
    if (amenities.hasDock) list.push('Dock');
    if (amenities.hasDoorman) list.push('Doorman');
    if (amenities.hasElevator) list.push('Elevator');
    if (amenities.hasFencedYard) list.push('Fenced Yard');
    if (amenities.hasFitnessCenter) list.push('Fitness Center');
    if (amenities.hasGarden) list.push('Garden');
    if (amenities.hasGatedEntry) list.push('Gated Entry');
    if (amenities.hasGreenHouse) list.push('Green House');
    if (amenities.hasHotTubSpa) list.push('Hot Tub Spa');
    if (amenities.hasHouseKeeping) list.push('House Keeping');
    if (amenities.hasIntercom) list.push('Intercom');
    if (amenities.hasJettedBathTub) list.push('Jetted Bath Tub');
    if (amenities.hasLawn) list.push('Lawn');
    if (amenities.hasNightPatrol) list.push('Night Patrol');
    if (amenities.hasOnSiteMaintenance) list.push('On Site Maintenance');
    if (amenities.hasOnSiteManagement) list.push('On Site Management');
    if (amenities.hasPackageReceiving) list.push('Package Receiving');
    if (amenities.hasPlayGround) list.push('Play Ground');
    if (amenities.hasPong) list.push('Pong');
    if (amenities.hasPorch) list.push('Porch');
    if (amenities.hasRaquetBallCourt) list.push('Raquet Ball Court');
    if (amenities.hasSauna) list.push('Sauna');
    if (amenities.hasSecuritySystem) list.push('Security System');
    if (amenities.hasSkylight) list.push('Skylight');
    if (amenities.hasSportsCourt) list.push('Sports Court');
    if (amenities.hasSprinklerSystem) list.push('Sprinkler System');
    if (amenities.hasSunDeck) list.push('Sun Deck');
    if (amenities.hasTennisCourt) list.push('Tennis Court');
    if (amenities.hasTVLounge) list.push('TV Lounge');
    if (amenities.hasVolleyBallCourt) list.push('Volley Ball Court');
    if (amenities.hasWetBar) list.push('Wet Bar');
    if (amenities.hasPool) list.push('Pool');

    return list;
  }

  private getRooms(specs: PropertySpecifications): string[] {
    const rooms: string[] = [];

    if (!specs) {
      return rooms;
    }

    if (specs.breakfastNook && specs.breakfastNook > 0) rooms.push('Breakfast Nook');
    if (specs.diningRoom && specs.diningRoom > 0) rooms.push('Dining');
    if (specs.familyRoom && specs.familyRoom > 0) rooms.push('Family');
    if (specs.laundryRoom && specs.laundryRoom > 0) rooms.push('Laundry');
    if (specs.library && specs.library > 0) rooms.push('Library');
    if (specs.masterBath && specs.masterBath > 0) rooms.push('Master Bath');
    if (specs.office && specs.office > 0) rooms.push('Office');
    if (specs.pantry && specs.pantry > 0) rooms.push('Pantry');
    if (specs.recreationRoom && specs.recreationRoom > 0) rooms.push('Recreation');
    if (specs.solariumAtrium && specs.solariumAtrium > 0) rooms.push('Solarium Atrium');
    if (specs.sunRoom && specs.sunRoom > 0) rooms.push('Sun');
    if (specs.workshop && specs.workshop > 0) rooms.push('Workshop');
    if (specs.walkInCloset && specs.walkInCloset > 0) rooms.push('Walk In Closet');

    return rooms;
  }

  private getFlooringFeatures(specs: PropertySpecifications): string[] {
    const flooring: string[] = [];

    if (!specs || !specs.floorCovering) {
      return flooring;
    }

    switch (specs.floorCovering) {
      case FloorCovering.CARPET:
        flooring.push('Carpet');
        break;
      case FloorCovering.CONCRETE:
        flooring.push('Concrete');
        break;
      case FloorCovering.HARDWOOD:
        flooring.push('Hardwood');
        break;
      case FloorCovering.LAMINATE:
        flooring.push('Laminate');
        break;
      case FloorCovering.SLATE:
        flooring.push('Slate');
        break;
      case FloorCovering.SOFTWOOD:
        flooring.push('Softwood');
        break;
      case FloorCovering.TILE:
        flooring.push('Tile');
        break;
      case FloorCovering.OTHER:
        flooring.push('Other');
        break;
    }

    return flooring;
  }

  private getZumperArchitectureStyle(style: ArchitectureStyle): string | null {
    switch (style) {
      case ArchitectureStyle.BUNGALOW:
        return 'Bungalow';
      case ArchitectureStyle.CAPE_COD:
        return 'Cape Cod';
      case ArchitectureStyle.COLONIAL:
        return 'Colonial';
      case ArchitectureStyle.CONTEMPORARY:
        return 'Contemporary';
      case ArchitectureStyle.CRAFTSMAN:
        return 'Craftsman';
      case ArchitectureStyle.FRENCH:
        return 'French';
      case ArchitectureStyle.GEORGIAN:
        return 'Georgian';
      case ArchitectureStyle.LOFT:
        return 'Loft';
      case ArchitectureStyle.MODERN:
        return 'Modern';
      case ArchitectureStyle.SANTA_FE_PUEBLO_STYLE:
        return 'Pueblo';
      case ArchitectureStyle.QUEEN_ANNE_VICTORIAN:
        return 'Queen Anne Victorian';
      case ArchitectureStyle.RANCH_RAMBLER:
        return 'Ranch';
      case ArchitectureStyle.SPANISH:
        return 'Spanish';
      case ArchitectureStyle.SPLIT_LEVEL:
        return 'Split-level';
      case ArchitectureStyle.TUDOR:
        return 'Tudor';
      case ArchitectureStyle.OTHER:
        return 'Other';
      default:
        return null;
    }
  }

  private getZumperExteriorType(type: ExteriorType): string | null {
    switch (type) {
      case ExteriorType.BRICK:
        return 'Brick';
      case ExteriorType.CEMENT_CONCRETE:
        return 'Concrete';
      case ExteriorType.COMPOSITION:
        return 'Composition';
      case ExteriorType.METAL:
        return 'Metal';
      case ExteriorType.SHINGLE:
        return 'Shingle';
      case ExteriorType.STONE:
        return 'Stone';
      case ExteriorType.STUCCO:
        return 'Stucco';
      case ExteriorType.VINYL:
        return 'Vinyl';
      case ExteriorType.WOOD:
      case ExteriorType.WOOD_PRODUCTS:
        return 'Wood';
      case ExteriorType.OTHER:
        return 'Other';
      default:
        return null;
    }
  }

  private getZumperRoofType(type: RoofType): string | null {
    switch (type) {
      case RoofType.ASPHALT:
        return 'Asphalt';
      case RoofType.BUILT_UP:
        return 'Built Up';
      case RoofType.COMPOSITION:
        return 'Composition';
      case RoofType.METAL:
        return 'Metal';
      case RoofType.SHAKE_SHINGLE:
        return 'Shingle';
      case RoofType.SLATE:
        return 'Slate';
      case RoofType.TILE:
        return 'Tile';
      case RoofType.OTHER:
        return 'Other';
      default:
        return null;
    }
  }

  private formatDateToISO8601(date: Date): string {
    // Format: "2021-09-08T00:36:00.000Z"
    return date.toISOString();
  }

  private formatToYYYYMMDD(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  async captureNewLead(lead: ZumperNewLead): Promise<void> {
    try {
      const genericLead = this.convertZumperLeadToGenericLead(lead);
      await this.leadsService.addLead(genericLead);
    } catch (error) {
      console.error('Error capturing new lead from Zumper', error);
    }
  }

  private convertZumperLeadToGenericLead(lead: ZumperNewLead): LeadDto {
    return {
      propertyId: lead.listingId,
      name: lead.name,
      email: lead.email,
      phone: lead.phone ? ParsingUtils.formatPhoneNumber(lead.phone) : undefined,
      message: lead.message,
      source: PropertyInquirySource.ZUMPER,
    };
  }
}
