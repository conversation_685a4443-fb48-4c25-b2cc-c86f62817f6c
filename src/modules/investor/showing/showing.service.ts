import { instanceT<PERSON><PERSON><PERSON> } from 'class-transformer';
import { In, <PERSON><PERSON><PERSON>, <PERSON><PERSON>han, Repository } from 'typeorm';

import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { TimezoneUtils } from '../../../utils/timezone.utils';
import { RentStageEnum } from '../../shared/communication/conversation/enums/rent-stage.enum';
import { OutboundCommunicationService } from '../../shared/communication/outbound-communication/outbound-communication.service';
import { PropertyInquiryService } from '../property-inquiry/property-inquiry.service';
import { convertRescheduleRequestToDto } from '../reschedule/model/reschedule-request.dto';
import { RescheduledShowingDto } from '../reschedule/model/rescheduled-showing.dto';
import { RescheduleRequestStatus } from '../reschedule/reschedule-request.entity';
import { RescheduleService } from '../reschedule/reschedule.service';
import { ShowingRequestStatus } from '../showing-request/enums/showing-request-status.enum';
import { convertShowingRequestToDto } from '../showing-request/showing-request.dto';
import { ShowingRequest } from '../showing-request/showing-request.entity';
import { ShowingRequestService } from '../showing-request/showing-request.service';
import { ShowingStatus } from './enums/showing-status.enum';
import { CancelShowingDto } from './model/cancel-showing.dto';
import { RescheduleShowingDto } from './model/reschedule-showing.dto';
import { ShowingDto } from './model/showing.dto';
import { Showing } from './showing.entity';
import { FilterUtils } from '../../../utils/filter.utils';
import { FollowUpService } from '../../shared/communication/follow-up/follow-up.service';
import { FollowUpTypeEnum } from '../../shared/communication/follow-up/enums/follow-up-type.enum';
import { ShowingAgent } from '../showing-agent/entities/showing-agent.entity';
import { RenterOutboundCommsService } from '../../renter/renter-communication/outbound-communication/renter-outbound-comms.service';
import { notifyRenterUnconfirmedShowingCanceledTemplate } from '../showing-request/prompts/notify-renter-unconfirmed-showing-canceled.template';
import { SlackCommunicationService } from '../../shared/communication/outbound-communication/slack/slack-communication.service';
import { SlackConvoMessageBuilder } from '../../shared/communication/outbound-communication/slack/slack-convo-message-builder.class';
import { PropertyService } from '../property/property/property.service';
import { ShowingCalendarService } from './showing-calendar.service';
import { calculateShowingEndTime } from './utils/calculate-showing-end-time.util';
import { TourType } from './enums/tour-type.enum';
import { TimeFilter } from '../../../shared/enums/time-filter.enum';
import { ActivityFeedService } from '../activity-feed/activity-feed.service';
import { ActivityFeedEventName } from '../activity-feed/activity-feed-event.name';

const showingReqStatusesThatCanBeCanceledOrRescheduled: ShowingRequestStatus[] = [
  ShowingRequestStatus.PENDING,
  ShowingRequestStatus.ACCEPTED,
];

@Injectable()
export class ShowingService {
  constructor(
    @InjectRepository(Showing)
    private readonly showingRepository: Repository<Showing>,
    @Inject(forwardRef(() => ShowingRequestService))
    private readonly showingRequestService: ShowingRequestService,
    private readonly notificationService: OutboundCommunicationService,
    @Inject(forwardRef(() => RescheduleService))
    private readonly rescheduleService: RescheduleService,
    private readonly propertyInquiryService: PropertyInquiryService,
    private readonly followUpService: FollowUpService,
    private readonly communicationService: OutboundCommunicationService,
    private readonly renterOutboundCommsService: RenterOutboundCommsService,
    private readonly slackCommunicationService: SlackCommunicationService,
    private readonly showingCalendarService: ShowingCalendarService,
    @Inject(forwardRef(() => PropertyService))
    private readonly propertyService: PropertyService,
    private readonly activityFeedService: ActivityFeedService,
  ) {}

  async cancelAllShowingsByProperty(propertyId: string, cancellationReason: string): Promise<void> {
    // find all showings that are not deleted and didn't happen yet
    const showings = await this.showingRepository.find({
      where: {
        property: { id: propertyId },
        status: In([ShowingStatus.PENDING, ShowingStatus.CONFIRMED]),
        startTime: MoreThan(new Date()),
      },
    });

    // cancel each showing
    for (const showing of showings) {
      await this.cancelShowing(showing.id, { cancelReason: cancellationReason });
    }
  }

  async findByShowingIdAndUserId(showingId: string, userId: string): Promise<Showing> {
    const query = this.showingRepository.createQueryBuilder('showing');

    return query
      .leftJoinAndSelect('showing.property', 'property')
      .leftJoinAndSelect('property.owner', 'propertyOwner')
      .leftJoinAndSelect('propertyOwner.user', 'propertyOwnerUser')
      .leftJoinAndSelect('property.company', 'company')
      .leftJoinAndSelect('showing.showingRequests', 'showingRequest', 'showingRequest.deletedAt IS NULL')
      .leftJoinAndSelect('showingRequest.renter', 'renter')
      .leftJoinAndSelect('renter.user', 'renterUser')
      .leftJoinAndSelect('property.location', 'location')
      .leftJoinAndSelect('showing.rescheduleRequests', 'rescheduleRequest')
      .leftJoinAndSelect('rescheduleRequest.renter', 'renterInRescheduleRequest')
      .leftJoinAndSelect('renterInRescheduleRequest.user', 'userInRescheduleRequest')
      .leftJoinAndSelect('showing.showingAgent', 'showingAgent')
      .where('showing.id = :showingId', { showingId })
      .andWhere('propertyOwnerUser.id = :userId', { userId })
      .getOne();
  }

  async findAllByCompany(companyId: string, filter: TimeFilter, onlyWithPendingRequests = true): Promise<Showing[]> {
    const query = this.showingRepository.createQueryBuilder('showing');
    if (!filter) {
      filter = TimeFilter.ALL_TIME;
    }
    const startDate = FilterUtils.getStartDateByFilter(filter);

    const queryBuilder = query
      .leftJoinAndSelect('showing.property', 'property')
      .leftJoinAndSelect('property.company', 'company')
      .leftJoinAndSelect('showing.showingRequests', 'showingRequest', 'showingRequest.deletedAt IS NULL')
      .leftJoinAndSelect('showingRequest.renter', 'renter')
      .leftJoinAndSelect('renter.user', 'renterUser')
      .leftJoinAndSelect('property.location', 'location')
      .leftJoinAndSelect(
        'showing.rescheduleRequests',
        'rescheduleRequest',
        'rescheduleRequest.status IN (:...rescheduleRequestStatuses)',
        {
          rescheduleRequestStatuses: [
            RescheduleRequestStatus.PENDING,
            RescheduleRequestStatus.CANCELED,
            RescheduleRequestStatus.DECLINED,
          ],
        },
      )
      .leftJoinAndSelect('rescheduleRequest.renter', 'renterInRescheduleRequest')
      .leftJoinAndSelect('renterInRescheduleRequest.user', 'userInRescheduleRequest')
      .leftJoinAndSelect('showing.showingAgent', 'showingAgent')
      .where('company.id = :companyId', { companyId })
      .andWhere('showing.createdAt >= :startDate', { startDate })
      .andWhere('property.deletedAt IS NULL')
      .andWhere('company.deletedAt IS NULL')
      .andWhere('showing.deletedAt IS NULL')
      .andWhere('showing.status != :status', {
        status: ShowingStatus.RESCHEDULED,
      })
      .andWhere('(showingRequest.status NOT IN (:...statuses) OR showingRequest.id IS NULL)', {
        statuses: [ShowingRequestStatus.RESCHEDULED],
      })
      .andWhere('(showingRequest.id IS NOT NULL OR rescheduleRequest.id IS NOT NULL)');

    if (onlyWithPendingRequests) {
      // Use INNER JOIN with a subquery for better performance
      queryBuilder.innerJoin(
        '(SELECT DISTINCT showingId FROM showing_request WHERE status = :pendingStatus AND deletedAt IS NULL)',
        'pendingShowings',
        'pendingShowings.showingId = showing.id',
        { pendingStatus: ShowingRequestStatus.PENDING },
      );
    }

    return queryBuilder.orderBy('showing.startTime', 'DESC').getMany();
  }

  async markCompletedShowings(): Promise<void> {
    const showings = await this.showingRepository.find({
      where: {
        status: ShowingStatus.CONFIRMED,
        endTime: LessThan(new Date()),
      },
      relations: ['showingRequests', 'property'],
    });

    showings.map(async (showing) => {
      showing.status = ShowingStatus.COMPLETED;
    });

    await this.showingRepository.save(showings);

    for (const showing of showings) {
      const showingRequests = (await showing.showingRequests).filter(
        ({ status }) => status === ShowingRequestStatus.ACCEPTED,
      );
      const property = await showing.property;
      showingRequests.map(async (showingRequest: ShowingRequest) => {
        const inquiry = await this.propertyInquiryService.findByRenterAndProperty(
          (await showingRequest.renter).id,
          (await showingRequest.property).id,
        );

        await this.propertyInquiryService.updateStageAndAddCorrespondingEvent(
          inquiry.id,
          RentStageEnum.SHOWING_COMPLETED,
        );
        const renter = await showingRequest.renter;

        this.activityFeedService.recordEvent(ActivityFeedEventName.SHOWING_COMPLETED, (await property.company).id, {
          userId: renter.user.id,
          propertyId: property.id,
          renterId: renter.id,
          showingId: showing.id,
        });

        await this.followUpService.createPostShowingFollowUps(await inquiry.conversation, inquiry);
      });
    }
  }

  async findById(id: string, withRelations = false): Promise<Showing> {
    return this.showingRepository.findOneOrFail({
      where: { id },
      relations: withRelations ? ['showingRequests'] : [],
    });
  }

  async sendBeforeShowingReminders(): Promise<void> {
    const showings = await this.showingRepository
      .createQueryBuilder('showing')
      .leftJoinAndSelect(
        'showing.showingRequests',
        'showingRequest',
        'showingRequest.status = :showingRequestStatus AND showingRequest.deletedAt IS NULL',
        { showingRequestStatus: ShowingRequestStatus.ACCEPTED },
      )
      .leftJoinAndSelect('showing.property', 'property')
      .leftJoinAndSelect('property.owner', 'owner')
      .leftJoinAndSelect('showingRequest.renter', 'renter')
      .leftJoinAndSelect('renter.user', 'renterUser')
      .where('showing.status = :showingStatus', {
        showingStatus: ShowingStatus.CONFIRMED,
      })
      .andWhere('showing.beforeShowingNotificationSent = false')
      .andWhere('showing."startTime" < :startTime', {
        startTime: new Date(new Date().getTime() + 120 * 60 * 1000),
      })
      .andWhere('showing."startTime" > :currentTime', {
        currentTime: new Date(new Date().getTime()),
      })
      .getMany();

    showings.map(async (showing) => {
      const property = await showing.property;
      const investor = await property.owner;
      const user = await investor.user;
      const propertyLocation = await property.location;

      await this.showingRepository.update(showing.id, {
        beforeShowingNotificationSent: true,
      });

      (await showing.showingRequests).map(async (showingRequest: ShowingRequest) => {
        const renter = await showingRequest.renter;
        const existingFollowUp = await this.followUpService.findFollowUpByUserAndType(
          renter.user.id,
          FollowUpTypeEnum.SHOWING_REMINDER_FOLLOW_UP,
        );

        if (!existingFollowUp) {
          await this.showingRequestService.sendRenterShowingReminderNotification(showingRequest);
        }
      });

      const showingDate = TimezoneUtils.convertDateToStringInCityTz(
        showing.startTime,
        propertyLocation.city,
        propertyLocation.timeZone,
      );

      const existingInvestorFollowUp = await this.followUpService.findFollowUpByUserAndType(
        user.id,
        FollowUpTypeEnum.INVESTOR_SHOWING_REMINDER_FOLLOW_UP,
      );

      if (!existingInvestorFollowUp) {
        await this.notificationService.sendUpcomingShowingReminderToInvestor(
          showing.id,
          propertyLocation,
          user,
          showingDate,
          showing.tourType,
        );
      }
    });
  }

  async create(showingData: Partial<Showing> = {}): Promise<Showing> {
    const showing: Showing = this.showingRepository.create({
      ...showingData,
    });
    return this.showingRepository.save(showing);
  }

  async save(id: string, showingData: Partial<Showing>): Promise<Showing> {
    const showing = await this.findById(id);
    Object.assign(showing, showingData);
    return await this.showingRepository.save(showing);
  }

  async update(id: string, showingData: Partial<Showing>): Promise<void> {
    await this.showingRepository.update(id, showingData);
  }

  async delete(id: string): Promise<void> {
    await this.showingRepository.softDelete(id);
  }

  findShowingsOverlappingWithTimeRange(propertyId: string, startTime: Date, endTime: Date): Promise<Showing[]> {
    return this.showingRepository
      .createQueryBuilder('showing')
      .where('showing.propertyId = :propertyId', { propertyId })
      .andWhere('showing.startTime < :endTime', { endTime })
      .andWhere('showing.endTime > :startTime', { startTime })
      .andWhere('showing.status IN (:...statuses)', {
        statuses: [ShowingStatus.PENDING, ShowingStatus.CONFIRMED],
      })
      .getMany();
  }

  async updateStatusBasedOnRequests(showing: Showing): Promise<ShowingStatus> {
    // Fetch the latest showing with all relations to ensure we have up-to-date data
    const freshShowing = await this.showingRepository.findOneOrFail({
      where: { id: showing.id },
      relations: ['showingRequests', 'rescheduleRequests'],
    });

    const showingRequests = await freshShowing.showingRequests;
    const rescheduleRequests = await freshShowing.rescheduleRequests;

    const hasAcceptedShowingRequest = showingRequests.some(({ status }) => status === ShowingRequestStatus.ACCEPTED);
    if (hasAcceptedShowingRequest) {
      showing.status = ShowingStatus.CONFIRMED;
      await this.update(freshShowing.id, { status: ShowingStatus.CONFIRMED });
      return ShowingStatus.CONFIRMED;
    }

    const hasPendingShowingRequest = showingRequests.some(({ status }) => status === ShowingRequestStatus.PENDING);
    if (hasPendingShowingRequest) {
      showing.status = ShowingStatus.PENDING;
      await this.update(freshShowing.id, { status: ShowingStatus.PENDING });
      return ShowingStatus.PENDING;
    }

    const hasPendingRescheduleRequest = rescheduleRequests.some(
      ({ status }) => status === RescheduleRequestStatus.PENDING,
    );
    if (hasPendingRescheduleRequest) {
      showing.status = ShowingStatus.PENDING;
      await this.update(freshShowing.id, { status: ShowingStatus.PENDING });
      return ShowingStatus.PENDING;
    }

    const hasRescheduledShowingRequest = showingRequests.some(
      ({ status }) => status === ShowingRequestStatus.RESCHEDULED,
    );
    const hasRescheduledRescheduleRequest = rescheduleRequests.some(
      ({ status }) => status === RescheduleRequestStatus.RESCHEDULED,
    );
    if (hasRescheduledShowingRequest || hasRescheduledRescheduleRequest) {
      showing.status = ShowingStatus.RESCHEDULED;
      await this.update(freshShowing.id, { status: ShowingStatus.RESCHEDULED });
      return ShowingStatus.RESCHEDULED;
    }

    const hasCanceledShowingRequest = showingRequests.some(
      ({ status }) =>
        status === ShowingRequestStatus.CANCELLED_BY_RENTER || status === ShowingRequestStatus.CANCELED_BY_INVESTOR,
    );

    const hasCanceledRescheduleRequest = rescheduleRequests.some(
      ({ status }) => status === RescheduleRequestStatus.CANCELED,
    );

    if (hasCanceledShowingRequest || hasCanceledRescheduleRequest) {
      showing.status = ShowingStatus.CANCELED;
      await this.update(freshShowing.id, { status: ShowingStatus.CANCELED });
      return ShowingStatus.CANCELED;
    }

    const hasDeclinedShowingRequest = showingRequests.some(({ status }) => status === ShowingRequestStatus.DECLINED);
    const hasDeclinedRescheduleRequest = rescheduleRequests.some(
      ({ status }) => status === RescheduleRequestStatus.DECLINED,
    );

    if (hasDeclinedShowingRequest || hasDeclinedRescheduleRequest) {
      showing.status = ShowingStatus.DECLINED;
      await this.update(freshShowing.id, { status: ShowingStatus.DECLINED });
      return ShowingStatus.DECLINED;
    }

    const hasExpiredShowingRequests = showingRequests.some(({ status }) => status === ShowingRequestStatus.EXPIRED);
    if (hasExpiredShowingRequests) {
      showing.status = ShowingStatus.EXPIRED;
      await this.update(freshShowing.id, { status: ShowingStatus.EXPIRED });
      return ShowingStatus.EXPIRED;
    }

    const hasNoShowingReqs = showingRequests.length === 0;
    const hasNoRescheduleReqs = rescheduleRequests.length === 0;
    const hasOnlyConfirmedRescheduleReqs =
      rescheduleRequests.length > 0 &&
      rescheduleRequests.every(({ status }) => status === RescheduleRequestStatus.CONFIRMED);

    if ((hasNoShowingReqs && hasNoRescheduleReqs) || hasOnlyConfirmedRescheduleReqs) {
      showing.status = ShowingStatus.RESCHEDULED;
      await this.update(freshShowing.id, { status: ShowingStatus.RESCHEDULED });
      await this.showingRepository.softDelete(freshShowing.id);
      return ShowingStatus.RESCHEDULED;
    }
  }

  async rescheduleShowing(showing: Showing, rescheduleDto: RescheduleShowingDto): Promise<RescheduledShowingDto> {
    const showingRequests = await showing.showingRequests;
    const showingRequestsToReschedule = showingRequests.filter((showingRequest) => {
      return showingReqStatusesThatCanBeCanceledOrRescheduled.includes(showingRequest.status);
    });

    let rescheduledShowingDto: RescheduledShowingDto;

    for (const showingRequest of showingRequestsToReschedule) {
      rescheduledShowingDto = await this.showingRequestService.rescheduleShowingRequest(
        showing.propertyId,
        {
          showingRequestId: showingRequest.id,
          startTime: new Date(rescheduleDto.startTime),
          comment: rescheduleDto.comment,
          showingAgentId: rescheduleDto.showingAgentId ?? showing.showingAgentId,
        },
        false,
      );
    }

    const rescheduleRequests = (await showing.rescheduleRequests).filter(
      ({ status }) => status === RescheduleRequestStatus.PENDING,
    );

    const property = await showing.property;
    for (const rescheduleRequest of rescheduleRequests) {
      rescheduledShowingDto = await this.rescheduleService.rescheduleRescheduleRequest(
        rescheduleRequest,
        property,
        new Date(rescheduleDto.startTime),
        await calculateShowingEndTime(property, rescheduleDto.startTime),
        rescheduleDto.comment,
        rescheduleDto.showingAgentId ?? showing.showingAgentId,
        showing.tourType,
      );
    }

    await this.update(showing.id, { status: ShowingStatus.RESCHEDULED });

    await this.propertyService.updateLastShowingAgent(showing.propertyId, rescheduleDto.showingAgentId);
    this.showingCalendarService.deleteEvent(showing);

    return rescheduledShowingDto;
  }

  async cancelShowing(showingId: string, cancelShowingDto: CancelShowingDto): Promise<void> {
    const showing = await this.findById(showingId, true);
    const showingRequests = await showing.showingRequests;
    const showingRequestsToCancel = showingRequests.filter((showingRequest) => {
      return showingReqStatusesThatCanBeCanceledOrRescheduled.includes(showingRequest.status);
    });

    for (const showingRequest of showingRequestsToCancel) {
      await this.showingRequestService.cancelRequest(
        showing.propertyId,
        showingRequest.id,
        cancelShowingDto.cancelReason,
        ShowingRequestStatus.CANCELED_BY_INVESTOR,
      );
    }

    const rescheduleRequests = (await showing.rescheduleRequests).filter(
      ({ status }) => status === RescheduleRequestStatus.PENDING,
    );

    for (const rescheduleRequest of rescheduleRequests) {
      await this.rescheduleService.cancelRescheduleRequestAsOwner(rescheduleRequest, cancelShowingDto.cancelReason);
    }

    this.showingCalendarService.deleteEvent(showing);

    await this.update(showingId, { status: ShowingStatus.CANCELED });
  }

  async assignShowingAgent(showingId: string, showingAgentId: string): Promise<void> {
    const showing = await this.findById(showingId);

    await this.showingRepository.update(showingId, { showingAgent: { id: showingAgentId } as ShowingAgent });
    await this.propertyService.updateLastShowingAgent(showing.propertyId, showingAgentId);
  }

  async convertToDto(
    showing: Showing,
    includeShowingRequests = false,
    includeRescheduleRequests = false,
    includeShowingAgent = false,
  ): Promise<ShowingDto> {
    const showingDto = <ShowingDto>instanceToPlain(showing, { excludeExtraneousValues: true });

    if (includeShowingRequests) {
      const showingRequests = await showing.showingRequests; // Load the lazy relationship once
      // Since mapping involves an async operation, map to an array of promises
      showingDto.showingRequests = await Promise.all(
        showingRequests.map(async (showingRequest: ShowingRequest) => {
          return convertShowingRequestToDto(showingRequest, true);
        }),
      );
    }

    if (includeRescheduleRequests) {
      const rescheduleRequests = await showing.rescheduleRequests;

      showingDto.rescheduleRequests = await Promise.all(
        rescheduleRequests.map(async (rescheduleRequest) => {
          return convertRescheduleRequestToDto(rescheduleRequest, true, false);
        }),
      );
    }

    if (includeShowingAgent) {
      showingDto.showingAgent = ShowingAgent.convertToDto(await showing.showingAgent);
    }

    const property = await showing.property;
    const location = await property.location;

    // TODO: Instead of loading a full property we should add only the needed fields
    showingDto.property = {
      id: property.id,
      displayName: property.displayName,
      coverImage: property.coverImage,
      location: {
        id: location.id,
        address: location.address,
        city: location.city,
        state: location.state,
        apartmentNumber: location.apartmentNumber,
        isAddressHidden: location.isAddressHidden,
        zip: location.zip,
        latitude: location.latitude,
        longitude: location.longitude,
        timeZone: location.timeZone,
      },
    };

    return showingDto;
  }

  async cancelUnconfirmedShowings(): Promise<void> {
    // Find all confirmed showings that are happening in less than 1 hour
    // and the renter has not confirmed attendance
    const cutoffTime = new Date();
    cutoffTime.setMinutes(cutoffTime.getMinutes() + 60); // 1 hour

    const showings = await this.showingRepository.find({
      where: {
        status: ShowingStatus.CONFIRMED,
        renterConfirmedAttendance: false,
        startTime: LessThan(cutoffTime),
        // Self-guided tours don't need cancellation because leasing agent won't be there
        tourType: In([TourType.IN_PERSON, TourType.VIRTUAL]),
      },
    });

    // Cancel each showing
    for (const showing of showings) {
      const property = await showing.property;
      const location = await property.location;
      const owner = await property.owner;
      const ownerUser = await owner.user;
      await this.update(showing.id, {
        status: ShowingStatus.CANCELED,
      });

      // Cancel all showing requests for this showing
      const showingRequests = await showing.showingRequests;
      for (const showingRequest of showingRequests) {
        const renter = await showingRequest.renter;
        const cancelReason = 'Renter did not confirm attendance';

        await this.showingRequestService.cancelRequest(
          property.id,
          showingRequest.id,
          cancelReason,
          ShowingRequestStatus.CANCELLED_BY_RENTER,
        );

        // Send notification to property owner
        await this.communicationService.sendRenterCancelledShowingNotification(
          property.id,
          ownerUser,
          renter,
          TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone),
          location.address,
          ownerUser.preferredCommunicationChannel,
        );

        // Send message to renter about the cancellation due to not confirming attendance
        const { message, conversation } = await this.renterOutboundCommsService.craftMessageAndSend({
          renter,
          propertyId: property.id,
          templateVariables: {
            propertyAddress: location.address,
            showingTime: TimezoneUtils.convertDateToStringInCityTz(showing.startTime, location.city, location.timeZone),
            renterName: renter.user.name.split(' ')[0],
          },
          template: notifyRenterUnconfirmedShowingCanceledTemplate,
        });

        // Send Slack notification
        await this.slackCommunicationService.sendMessageToConvosChannel(
          new SlackConvoMessageBuilder()
            .appendTextLine('❌ Showing was automatically canceled because renter did not confirm attendance')
            .appendEmptyLine()
            .appendTalloMessageAsBullet(message)
            .build(),
          conversation,
        );
      }
    }
  }
}
